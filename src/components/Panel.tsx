"use client";
import React, { useState, useRef } from "react";
import type { Revision } from "@/app/api/recognize-instructions/route";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  ChevronUp,
  ChevronDown,
  X,
  MoveRight,
  Square,
  SquareCheck,
  Plus,
  Save,
} from "lucide-react";

interface ResultViewerProps {
  analysis: Revision | null;
  onAnalysisUpdate?: (updatedAnalysis: Revision) => void;
}

export default function Panel({
  analysis,
  onAnalysisUpdate,
}: ResultViewerProps) {
  const [height, setHeight] = useState(250);
  const [minimized, setMinimized] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const isResizing = useRef(false);

  // Handle panel resize
  const onMouseDown = (e: React.MouseEvent) => {
    isResizing.current = true;
    const startY = e.clientY;
    const startHeight = height;

    const onMouseMove = (moveEvent: MouseEvent) => {
      if (!isResizing.current) return;
      const delta = moveEvent.clientY - startY;
      setHeight(Math.max(100, startHeight - delta));
    };

    const onMouseUp = () => {
      isResizing.current = false;
      window.removeEventListener("mousemove", onMouseMove);
      window.removeEventListener("mouseup", onMouseUp);
    };

    window.addEventListener("mousemove", onMouseMove);
    window.addEventListener("mouseup", onMouseUp);
  };
  const handleTypeChange = (
    index: number,
    newType: "insert" | "delete" | "replace"
  ) => {
    if (!analysis) return;

    const updatedInstructions = [...analysis.instructions];
    const currentInstruction = updatedInstructions[index];

    // Create new instruction with the new type, preserving common fields
    const baseInstruction = {
      type: newType,
      position: currentInstruction.position,
      applied: currentInstruction.applied,
    };

    // Handle type-specific fields
    if (newType === "insert") {
      updatedInstructions[index] = {
        ...baseInstruction,
        type: "insert" as const,
        text:
          currentInstruction.type === "delete"
            ? currentInstruction.text
            : currentInstruction.type === "replace"
            ? currentInstruction.after
            : "",
      };
    } else if (newType === "delete") {
      updatedInstructions[index] = {
        ...baseInstruction,
        type: "delete" as const,
        text:
          currentInstruction.type === "insert"
            ? currentInstruction.text
            : currentInstruction.type === "replace"
            ? currentInstruction.before
            : "",
      };
    } else {
      updatedInstructions[index] = {
        ...baseInstruction,
        type: "replace" as const,
        before:
          currentInstruction.type === "delete" ? currentInstruction.text : "",
        after:
          currentInstruction.type === "insert" ? currentInstruction.text : "",
      };
    }

    const updatedAnalysis = { ...analysis, instructions: updatedInstructions };
    setHasUnsavedChanges(true);

    if (onAnalysisUpdate) {
      onAnalysisUpdate(updatedAnalysis);
    }
  };

  const handleFieldChange = (index: number, field: string, value: string) => {
    if (!analysis) return;

    const updatedInstructions = [...analysis.instructions];
    updatedInstructions[index] = {
      ...updatedInstructions[index],
      [field]: value,
    };

    const updatedAnalysis = { ...analysis, instructions: updatedInstructions };
    setHasUnsavedChanges(true);

    if (onAnalysisUpdate) {
      onAnalysisUpdate(updatedAnalysis);
    }
  };

  const handleDelete = (index: number) => {
    if (!analysis) return;

    const updatedInstructions = analysis.instructions.filter(
      (_, i) => i !== index
    );
    const updatedAnalysis = { ...analysis, instructions: updatedInstructions };
    setHasUnsavedChanges(true);

    if (onAnalysisUpdate) {
      onAnalysisUpdate(updatedAnalysis);
    }
  };

  const handleToggleApplied = (index: number) => {
    if (!analysis) return;

    const updatedInstructions = [...analysis.instructions];
    updatedInstructions[index] = {
      ...updatedInstructions[index],
      applied: !updatedInstructions[index].applied,
    };

    const updatedAnalysis = { ...analysis, instructions: updatedInstructions };
    setHasUnsavedChanges(true);

    if (onAnalysisUpdate) {
      onAnalysisUpdate(updatedAnalysis);
    }
  };

  const handleAddInstruction = () => {
    if (!analysis) return;

    const newInstruction = {
      type: "insert" as const,
      position: "",
      text: "",
      applied: false,
    };

    const updatedInstructions = [...analysis.instructions, newInstruction];
    const updatedAnalysis = { ...analysis, instructions: updatedInstructions };
    setHasUnsavedChanges(true);

    if (onAnalysisUpdate) {
      onAnalysisUpdate(updatedAnalysis);
    }
  };

  const handleSaveChanges = () => {
    setHasUnsavedChanges(false);
    // Additional save logic can be added here if needed
  };

  if (minimized) {
    return (
      <div className="fixed left-0 right-0 bottom-0 h-8 bg-gray-800 text-white flex items-center z-50">
        <span className="ml-4">認識結果</span>
        <Button
          onClick={() => setMinimized(false)}
          className="ml-auto mr-4"
          size="sm"
          variant="ghost"
        >
          <ChevronUp className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div
      className="fixed left-0 right-0 bottom-0 flex flex-col "
      style={{
        height,
        userSelect: isResizing.current ? "none" : "auto",
      }}
    >
      {/* Resize Handle */}
      <div
        className="h-1 cursor-ns-resize bg-gray-700"
        onMouseDown={onMouseDown}
      />
      {/* Panel Header */}
      <div className="flex items-center bg-background h-10 border-y px-4">
        <span className="font-bold text-lg">認識結果</span>
        {hasUnsavedChanges && (
          <span className="ml-2 text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded-full">
            未保存の変更
          </span>
        )}
        <div className="flex-1" />
        <div className="flex gap-2">
          <Button
            onClick={handleAddInstruction}
            size="sm"
            variant="outline"
            className="h-8"
          >
            <Plus className="h-4 w-4 mr-1" />
            追加
          </Button>
          {hasUnsavedChanges && (
            <Button
              onClick={handleSaveChanges}
              size="sm"
              variant="default"
              className="h-8"
            >
              <Save className="h-4 w-4 mr-1" />
              保存
            </Button>
          )}
          <Button
            onClick={() => setMinimized(true)}
            size="sm"
            variant="ghost"
            className="h-8"
          >
            <ChevronDown className="h-4 w-4" />
          </Button>
        </div>
      </div>
      {/* Panel Content */}
      <ScrollArea className="bg-background h-full">
        {analysis?.instructions.length === 0 ? (
          <div className="flex items-center justify-center h-32 text-muted-foreground">
            <div className="text-center">
              <p className="text-sm">修正指示がありません</p>
              <Button
                onClick={handleAddInstruction}
                size="sm"
                variant="outline"
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-1" />
                新しい指示を追加
              </Button>
            </div>
          </div>
        ) : (
          analysis?.instructions.map((instruction, index) => (
            <div key={index} className="border-b p-6">
              {/* Header with status and controls */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleToggleApplied(index)}
                    className="p-1 h-auto"
                  >
                    {instruction.applied ? (
                      <SquareCheck className="w-5 h-5 text-green-600" />
                    ) : (
                      <Square className="w-5 h-5 text-gray-400 hover:text-gray-600" />
                    )}
                  </Button>
                  <span className="text-sm font-medium text-muted-foreground">
                    指示 #{index + 1}
                  </span>
                  {instruction.applied && (
                    <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                      適用済み
                    </span>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDelete(index)}
                  className="text-red-500 hover:text-red-600 hover:bg-red-50"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {/* Type selector and content */}
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                {/* Type selector */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">修正タイプ</Label>
                  <Select
                    value={instruction.type}
                    onValueChange={(value) =>
                      handleTypeChange(
                        index,
                        value as "insert" | "delete" | "replace"
                      )
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="修正タイプを選択" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>修正タイプ</SelectLabel>
                        <SelectItem value="insert">挿入</SelectItem>
                        <SelectItem value="delete">削除</SelectItem>
                        <SelectItem value="replace">置換</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>

                {/* Position field */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">適用箇所</Label>
                  <Textarea
                    value={instruction.position}
                    onChange={(e) =>
                      handleFieldChange(index, "position", e.target.value)
                    }
                    placeholder="適用する箇所を入力..."
                    className="min-h-[80px] resize-none"
                  />
                </div>
                {/* Content fields based on type */}
                {instruction.type === "insert" ? (
                  <div className="lg:col-span-2 space-y-2">
                    <Label className="text-sm font-medium">挿入テキスト</Label>
                    <Textarea
                      value={instruction.text}
                      onChange={(e) =>
                        handleFieldChange(index, "text", e.target.value)
                      }
                      placeholder="挿入するテキストを入力..."
                      className="min-h-[80px] resize-none"
                    />
                  </div>
                ) : instruction.type === "delete" ? (
                  <div className="lg:col-span-2 space-y-2">
                    <Label className="text-sm font-medium">削除テキスト</Label>
                    <Textarea
                      value={instruction.text}
                      onChange={(e) =>
                        handleFieldChange(index, "text", e.target.value)
                      }
                      placeholder="削除するテキストを入力..."
                      className="min-h-[80px] resize-none"
                    />
                  </div>
                ) : (
                  <>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">
                        変更前テキスト
                      </Label>
                      <Textarea
                        value={instruction.before}
                        onChange={(e) =>
                          handleFieldChange(index, "before", e.target.value)
                        }
                        placeholder="変更前のテキストを入力..."
                        className="min-h-[80px] resize-none"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">
                        変更後テキスト
                      </Label>
                      <div className="relative">
                        <Textarea
                          value={instruction.after}
                          onChange={(e) =>
                            handleFieldChange(index, "after", e.target.value)
                          }
                          placeholder="変更後のテキストを入力..."
                          className="min-h-[80px] resize-none"
                        />
                        <MoveRight className="absolute top-2 right-2 w-4 h-4 text-muted-foreground" />
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          ))
        )}
      </ScrollArea>
    </div>
  );
}
