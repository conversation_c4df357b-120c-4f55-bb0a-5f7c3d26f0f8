"use client";
import React, { useState, useRef, useEffect } from "react";
import type { Revision } from "@/app/api/recognize-instructions/route";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import {
  ChevronUp,
  ChevronDown,
  X,
  MoveRight,
  Square,
  SquareCheck,
} from "lucide-react";

interface ResultViewerProps {
  analysis: Revision | null;
  onAnalysisUpdate?: (updatedAnalysis: Revision) => void;
}

export default function Panel({
  analysis,
  onAnalysisUpdate,
}: ResultViewerProps) {
  const [height, setHeight] = useState(250);
  const [minimized, setMinimized] = useState(false);
  const isResizing = useRef(false);

  // Handle panel resize
  const onMouseDown = (e: React.MouseEvent) => {
    isResizing.current = true;
    const startY = e.clientY;
    const startHeight = height;

    const onMouseMove = (moveEvent: MouseEvent) => {
      if (!isResizing.current) return;
      const delta = moveEvent.clientY - startY;
      setHeight(Math.max(100, startHeight - delta));
    };

    const onMouseUp = () => {
      isResizing.current = false;
      window.removeEventListener("mousemove", onMouseMove);
      window.removeEventListener("mouseup", onMouseUp);
    };

    window.addEventListener("mousemove", onMouseMove);
    window.addEventListener("mouseup", onMouseUp);
  };
  const handleTypeChange = (
    index: number,
    newType: "insert" | "delete" | "replace"
  ) => {
    // const newScripts = [...instructions];
    // newScripts[index] = { ...newScripts[index], type: newType };
    // setInstructions(newScripts);
    // // Notify parent component of the change
    // if (onAnalysisUpdate) {
    //   onAnalysisUpdate({ instructions: newScripts });
    // }
  };
  const handleLinesChange = (index: number, newLines: string) => {
    // const newScripts = [...instructions];
    // newScripts[index] = { ...newScripts[index], content: newLines };
    // setInstructions(newScripts);
    // // Notify parent component of the change
    // if (onAnalysisUpdate) {
    //   onAnalysisUpdate({ instructions: newScripts });
    // }
  };
  const handleDelete = (index: number) => {
    // const newScripts = instructions.filter((_, i) => i !== index);
    // setInstructions(newScripts);
    // // Notify parent component of the change
    // if (onAnalysisUpdate) {
    //   onAnalysisUpdate({ instructions: newScripts });
    // }
  };

  if (minimized) {
    return (
      <div className="fixed left-0 right-0 bottom-0 h-8 bg-gray-800 text-white flex items-center z-50">
        <span className="ml-4">Panel (minimized)</span>
        <Button
          onClick={() => setMinimized(false)}
          className="ml-auto mr-4"
          size="sm"
          variant="ghost"
        >
          <ChevronUp className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div
      className="fixed left-0 right-0 bottom-0 flex flex-col "
      style={{
        height,
        userSelect: isResizing.current ? "none" : "auto",
      }}
    >
      {/* Resize Handle */}
      <div
        className="h-1 cursor-ns-resize bg-gray-700"
        onMouseDown={onMouseDown}
      />
      {/* Panel Header */}
      <div className="flex items-center bg-background h-8 border-y">
        <span className="pl-4 font-bold">認識結果</span>
        <div className="flex-1" />
        <div className="flex gap-2 mr-2">
          <Button onClick={() => setMinimized(true)} size="sm" variant="ghost">
            <ChevronDown className="h-4 w-4" />
          </Button>
        </div>
      </div>
      {/* Panel Content */}
      <ScrollArea className="bg-background h-full">
        {analysis?.instructions.map((instruction, index) => (
          <div key={index} className="border-b p-4 flex justify-between">
            <div className="flex flex-col">
              {instruction.applied ? (
                <SquareCheck className="w-4 h-4 text-green-500" />
              ) : (
                <Square className="w-4 h-4 text-gray-500" />
              )}
              <Select
                value={instruction.type}
                onValueChange={(value) =>
                  handleTypeChange(
                    index,
                    value as "insert" | "delete" | "replace"
                  )
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="修正タイプを選択" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>修正タイプ</SelectLabel>
                    <SelectItem value="insert">挿入</SelectItem>
                    <SelectItem value="delete">削除</SelectItem>
                    <SelectItem value="replace">置換</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col">
              <h4 className="text-sm font-bold my-2">適用箇所</h4>
              <Textarea
                value={instruction.position}
                onChange={(e) => handleLinesChange(index, e.target.value)}
              />
            </div>
            {instruction.type === "insert" ? (
              <div className="flex flex-col">
                <h4 className="text-sm font-bold my-2">挿入テキスト</h4>
                <Textarea
                  value={instruction.text}
                  onChange={(e) => handleLinesChange(index, e.target.value)}
                />
              </div>
            ) : instruction.type === "delete" ? (
              <div className="flex flex-col">
                <h4 className="text-sm font-bold my-2">削除テキスト</h4>
                <Textarea
                  value={instruction.text}
                  onChange={(e) => handleLinesChange(index, e.target.value)}
                />
              </div>
            ) : (
              <>
                <div className="flex flex-col">
                  <h4 className="text-sm font-bold my-2">変更前テキスト</h4>
                  <Textarea
                    value={instruction.before}
                    onChange={(e) => handleLinesChange(index, e.target.value)}
                  />
                </div>
                <MoveRight className="w-4 h-4 p-2" />
                <div className="flex flex-col">
                  <h4 className="text-sm font-bold my-2">変更後テキスト</h4>
                  <Textarea
                    value={instruction.after}
                    onChange={(e) => handleLinesChange(index, e.target.value)}
                  />
                </div>
              </>
            )}
            <div className="flex flex-col">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDelete(index)}
              >
                <X className="w-4 h-4 text-red-500 hover:text-red-600" />
              </Button>
            </div>
          </div>
        ))}
      </ScrollArea>
    </div>
  );
}
